# Terraform Infrastructure Deployment **IMPORTANT** 

**(Please Use live2 branch only as of now , This is backend deployment for frontend deployment you need to use different repo)**

This repository contains Terraform/Terragrunt code for deploying infrastructure to AWS. The deployment process is automated through a CI/CD pipeline.

## Prerequisites

1. **ACM Certificate**: Create an ACM certificate for your domain in your respective region (example: "*.phynd.games"). If it already exists, you can skip this step.

2. **Project Name**: Use a short length name for your Project **<your-project>** up to 15-16 characters.

3. Clone the run.sh file in your local machine and update the BITBUCKET credentials.

## Deployment Methods

### Method 1: Using run.sh (Recommended)

1. **Set up environment variables**: Create a `run.sh` file or export the following variables:
   ```bash
   export BITBUCKET_WORKSPACE=xstrela
   export BITBUCKET_REPO=xstrela-terragrunt-infra
   export BITBUCKET_USERNAME=mayank211
   export BITBUCKET_APP_PASSWORD=ATBBrT4Kvus7WFfEqvjVRLGdCTTc74701E1F
   ```

2. **Run the deployment command**:
   ```bash
   ./run.sh --deploy <env name> --project <project name> --region <region name> --action <deploy/destroy>
   ```

   **Example**:
   ```bash
   ./run.sh --deploy dev --project newbangaluru --region us-west-2 --action deploy
   ```

   **Parameters**:
   - `<env name>`: Environment name (e.g., dev, qa, uat, prod)
   - `<project name>`: Your project name (e.g., newbangaluru)
   - `<region name>`: AWS region (e.g., us-west-2, us-east-1)
   - `<deploy/destroy>`: Action to perform - use `deploy` to apply infrastructure or `destroy` to tear it down


## How It Works

When you run the `run.sh` script with the deploy action, it will:

1. Check if the specified environment directories exist
2. Create them if they don't exist, using the default project as a template
3. Generate environment-specific configuration files
4. Create or verify S3 buckets for Terraform state storage
5. Create or verify DynamoDB tables for state locking
6. Run Terragrunt to apply infrastructure changes
7. Generate and store deployment artifacts

## Destroying Infrastructure

To destroy infrastructure, simply change the action parameter to `destroy`:

```bash
./run.sh --deploy <env name> --project <project name> --region <region name> --action destroy
```

**Example**:
```bash
./run.sh --deploy dev --project newbangaluru --region us-west-2 --action destroy
```

## Example Usage

**Deploy infrastructure**:
```bash
export BITBUCKET_WORKSPACE=xstrela
export BITBUCKET_REPO=xstrela-terragrunt-infra
export BITBUCKET_USERNAME=mayank211
export BITBUCKET_APP_PASSWORD=ATBBrT4Kvus7WFfEqvjVRLGdCTTc74701E1F

./run.sh --deploy dev --project newbangaluru --region us-west-2 --action deploy
```

**Destroy infrastructure**:
```bash
./run.sh --deploy dev --project newbangaluru --region us-west-2 --action destroy
```

## Notes

- Make sure you have the required environment variables set before running the script
- The script creates S3 buckets and DynamoDB tables for Terraform state management
- Always verify your parameters before running the deployment
- The script will automatically handle directory creation and configuration setup