# Terraform Infrastructure Deployment **IMPORTANT** 

**(Please Use live-branch  branch only as of now , This is backend deployment for frontend deployment you need to use different repo)**

This repository contains Terraform/Terragrunt code for deploying infrastructure to AWS. The deployment process is automated through a CI/CD pipeline.

## Prerequisites

1. **ACM Certificate**: Create an ACM certificate for your domain in your respective region (example: "*.phynd.games"). If it already exists, you can skip this step.

2. **Project Name**: Use a short length name for your Project **<your-project>** up to 15-16 characters.

3. Clone the run.sh file in your local machine and update the BITBUCKET credentials.

## Deployment Methods

### Method 1: Using run.sh (Recommended)

1. **Set up environment variables**: Create a `run.sh` file or export the following variables:
   ```bash
   export BITBUCKET_WORKSPACE=xstrela
   export BITBUCKET_REPO=xstrela-terragrunt-infra
   export BITBUCKET_USERNAME=mayank211
   export BITBUCKET_APP_PASSWORD=ATBBrT4Kvus7WFfEqvjVRLGdCTTc74701E1F
   ```

2. **Run the deployment command**:
   ```bash
   ./run.sh --deploy <env name> --project <project name> --region <region name> --action <deploy/destroy> --branch <branch name> --developer <developer name>
   ```

   **Example**:
   ```bash
   ./run.sh --deploy dev --project newbangaluru --region us-west-2 --action deploy --branch feature-auth --developer john.doe
   ```

   **Parameters**:
   - `<env name>`: Environment name (e.g., dev, qa, uat, prod)
   - `<project name>`: Your project name (e.g., newbangaluru)
   - `<region name>`: AWS region (e.g., us-west-2, us-east-1)
   - `<deploy/destroy>`: Action to perform - use `deploy` to apply infrastructure or `destroy` to tear it down
   - `<branch name>`: Git branch name for resource naming and tracking (e.g., feature-auth, main, develop)
   - `<developer name>`: Developer identifier for resource tagging and ownership tracking (e.g., john.doe, jane.smith)


## Branch and Developer Arguments

The `--branch` and `--developer` arguments serve important purposes in resource management:

### Branch Argument (`--branch`)
- **Resource Naming**: Used in resource naming conventions to create unique identifiers
- **Environment Isolation**: Helps isolate resources by branch for feature development
- **Conditional Logic**: When `environment == branch`, the branch suffix is automatically removed from resource names to avoid duplication (e.g., `dev-myproject-dev` becomes `dev-myproject`)

### Developer Argument (`--developer`)
- **Resource Tagging**: Automatically tags all resources with the developer identifier
- **Ownership Tracking**: Helps track who created/owns specific infrastructure resources
- **Cost Attribution**: Enables cost tracking and attribution by developer
- **Debugging**: Makes it easier to identify who deployed specific resources for troubleshooting

### Example Resource Naming
- **With different environment and branch**: `dev-myproject-feature-auth`
- **With same environment and branch**: `dev-myproject` (branch suffix removed automatically)

## How It Works

When you run the `run.sh` script with the deploy action, it will:

1. Check if the specified environment directories exist
2. Create them if they don't exist, using the default project as a template
3. Generate environment-specific configuration files
4. Create or verify S3 buckets for Terraform state storage
5. Create or verify DynamoDB tables for state locking
6. Run Terragrunt to apply infrastructure changes
7. Generate and store deployment artifacts

## Destroying Infrastructure

To destroy infrastructure, simply change the action parameter to `destroy`:

```bash
./run.sh --deploy <env name> --project <project name> --region <region name> --action destroy --branch <branch name> --developer <developer name>
```

**Example**:
```bash
./run.sh --deploy dev --project newbangaluru --region us-west-2 --action destroy --branch feature-auth --developer john.doe
```

## Example Usage

**Deploy infrastructure**:
```bash
export BITBUCKET_WORKSPACE=xstrela
export BITBUCKET_REPO=xstrela-terragrunt-infra
export BITBUCKET_USERNAME=mayank211
export BITBUCKET_APP_PASSWORD=ATBBrT4Kvus7WFfEqvjVRLGdCTTc74701E1F

./run.sh --deploy dev --project newbangaluru --region us-west-2 --action deploy --branch feature-auth --developer john.doe
```

**Destroy infrastructure**:
```bash
./run.sh --deploy dev --project newbangaluru --region us-west-2 --action destroy --branch feature-auth --developer john.doe
```

## Notes

- Make sure you have the required environment variables set before running the script
- The script creates S3 buckets and DynamoDB tables for Terraform state management
- Always verify your parameters before running the deployment
- The script will automatically handle directory creation and configuration setup