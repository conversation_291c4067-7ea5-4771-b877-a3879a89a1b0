locals {
  config = jsondecode(file("${get_parent_terragrunt_dir()}/env.json"))
  environment = local.config.env
  aws_region = local.config.aws_region
  project_name = local.config.project_name
  branch = local.config.branch
  developer = local.config.developer
  bucket_name = local.environment == local.branch ? "${local.project_name}-${local.environment}-${local.aws_region}" : "${local.project_name}-${local.environment}-${local.aws_region}-${local.branch}"
}

remote_state {
  backend = "s3"
  config = {
    bucket         = local.bucket_name
    region         = local.aws_region
    key            = "${local.developer}/${path_relative_to_include()}/terraform.tfstate"
    dynamodb_table = "${local.project_name}-${local.environment}-${local.developer}-${local.branch}-${local.aws_region}-dynamodb"
    encrypt        = true
    profile        = "deployment"
  }
  generate = {
    path      = "backend.tf"
    if_exists = "overwrite_terragrunt"
  }
}

generate "versions" {
  path      = "versions.tf"
  if_exists = "overwrite"
  contents  = <<EOF
terraform {
  required_version = ">= 1.3"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = ">= 5.35"
    }
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = ">= 2.16.0"
    }
    helm = {
      source  = "hashicorp/helm"
      version = ">= 2.6.0"
    }
    kubectl = {
      source  = "gavinbunney/kubectl"
      version = "~> 1.14.0"
    }
    cloudflare = {
      source  = "cloudflare/cloudflare"
      version = "4.52.0"
    }
  }
}
provider "aws" {
  region  = "${local.config.aws_region}"
  profile = "deployment"
}
EOF
}
