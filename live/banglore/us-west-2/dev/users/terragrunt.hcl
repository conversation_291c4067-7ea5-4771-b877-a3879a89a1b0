include "root" {
  path = find_in_parent_folders()
  expose = true
}

# generate = local.gen_provider.generate

locals {
  config = jsondecode(file(find_in_parent_folders("env.json")))
  environment = local.config.env
  aws_region = local.config.aws_region
  project_name = local.config.project_name
  branch = local.config.branch
  developer = local.config.developer
  domain = "phynd.games"
  # gen_provider   = read_terragrunt_config("${get_repo_root()}/live/terragrunt.hcl")

}

terraform {
  # source = "../../../../../modules/vpc"
#   source = "git::ssh://*****************/xstrela/xstrela-terragrunt-modules.git//modules/vpc?ref=v1.0.0"
   source = "git::ssh://*****************/xstrela/xstrela-terragrunt-modules.git//modules/amplify?ref=v1.0.0"
}

inputs = {
  environment = local.branch
  app_name = "${local.environment}-${local.project_name}-${local.branch}-frontend"
  repository = "https://bitbucket.org/xstrela/web-react-frontend-xstrela" 
  framework = "React"
  bitbucket_workspace= "xstrela"
  bitbucket_oauth_client_id = get_env("bitbucket_oauth_client_id","")
  bitbucket_oauth_secret = get_env("bitbucket_oauth_secret","")
  environments = {
  users = {
    branch_name = local.branch
    enable_auto_build = true
    stage = "DEVELOPMENT"
    framework = "React"
    enable_notification = true
    enable_performance_mode = true
    webhook_enabled = true
  }
  }
    domains = {
    users = {
        domain_name = join(".", [local.branch, local.domain])
        sub_domain = [
        {
            prefix      = "" # qa.otherdomain.com
            branch_name = local.branch
        }
        ]
    }
    }

}
