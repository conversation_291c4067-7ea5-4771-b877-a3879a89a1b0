locals {
  config = jsondecode(file(find_in_parent_folders("env.json")))
  environment = local.config.env
  aws_region = local.config.aws_region
  project_name = local.config.project_name
  branch = local.config.branch
  developer = local.config.developer
  eks_cluster_name = "${local.environment}-${local.project_name}-${local.branch}-eks"
 
}

include "root" {
  path = find_in_parent_folders()
  expose = true
}

dependencies {
  paths = ["../eks","../datasources"]
}
dependency "eks" {
  config_path = "../eks"
  mock_outputs = {
    cluster_id = "temporary-dummy-id"
    cluster_identity_oidc_issuer_arn ="eeededed"
    cluster_identity_oidc_issuer = "eedeefe"
    cluster_oidc_issuer_url = "dddde"
    endpoint ="eeeddde"
    cluster_certificate_authority_data = "eeeee"
  }
#   skip_outputs = false
  mock_outputs_allowed_terraform_commands = ["init", "validate", "plan", "destroy"]
  
}
 dependency "data" {
    config_path = "../datasources"
    mock_outputs = {
    aws_region  = local.config.aws_region
  }
  mock_outputs_allowed_terraform_commands = ["validate", "plan", "terragrunt-info", "show", "destroy"]
 }

terraform {
  #source = "../../../../../modules/autoscaling"
  source = "git::ssh://*****************/xstrela/xstrela-terragrunt-modules.git//modules/autoscaling?ref=v1.0.0"
} 


inputs = {
  cluster_name    = local.eks_cluster_name
  cluster_identity_oidc_issuer_arn = dependency.eks.outputs.cluster_identity_oidc_issuer_arn
  cluster_identity_oidc_issuer = dependency.eks.outputs.cluster_oidc_issuer_url
  aws_region        = dependency.data.outputs.aws_region
  
}



generate "provider-local" {
  path      = "provider-local.tf"
  if_exists = "overwrite"
  contents  = <<EOF

provider "kubernetes" {
  host                   = data.aws_eks_cluster.cluster.endpoint
  cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority[0].data)
  exec {
    api_version = "client.authentication.k8s.io/v1beta1"
    command     = "aws"
    args        = ["eks", "get-token", "--cluster-name", "${local.eks_cluster_name}"]
  }
}

provider "helm" {
  kubernetes  = {
    host                   = data.aws_eks_cluster.cluster.endpoint
    cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority[0].data)
    exec = {
      api_version = "client.authentication.k8s.io/v1beta1"
      command     = "aws"
      args        = ["eks", "get-token", "--cluster-name", "${local.eks_cluster_name}"]
    }
  }
}

data "aws_eks_cluster" "cluster" {
  name = "${local.eks_cluster_name}"
}

data "aws_eks_cluster_auth" "cluster" {
  name = "${local.eks_cluster_name}"
}
EOF
}