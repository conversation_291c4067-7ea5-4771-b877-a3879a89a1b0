output "vpc_id" {
  description = "The ID of the VPC"
  value       =   aws_vpc.vpc[0].id
}

output "vpc_arn" {
  description = "The ARN of the VPC"
  value       = try(aws_vpc.vpc[*].arn, "")
}

output "vpc_cidr_block" {
  description = "The CIDR block of the VPC"
  value       = try(aws_vpc.vpc[*].cidr_block, "")
}


output "private_subnets" {
  description = "List of IDs of private subnets"
  value       = try(aws_subnet.private[*].id,"")
}

output "private_subnet_arns" {
  description = "List of ARNs of private subnets"
  value       = try(aws_subnet.private[*].arn,"")
}

output "private_subnets_cidr_blocks" {
  description = "List of cidr_blocks of private subnets"
  value       = try(aws_subnet.private[*].cidr_block,"")
}

output "private_data_subnets" {
  description = "List of IDs of private  data subnets"
  value       = try(aws_subnet.private_data[*].id,"")
}

output "private_data_subnet_arns" {
  description = "List of ARNs of private  data subnets"
  value       = try(aws_subnet.private_data[*].arn,"")
}

output "private_data_subnets_cidr_blocks" {
  description = "List of cidr_blocks of private  data subnets"
  value       = try(aws_subnet.private_data[*].cidr_block,"")
}


output "public_subnets" {
  description = "List of IDs of public subnets"
  value       = try(aws_subnet.public[*].id,"")
}

output "public_subnet_arns" {
  description = "List of ARNs of public subnets"
  value       = try(aws_subnet.public[*].arn,"")
}

output "public_subnets_cidr_blocks" {
  description = "List of cidr_blocks of public subnets"
  value       = try(aws_subnet.public[*].cidr_block,"")
}

output "bastion_instance_id" {
  value = aws_instance.e1[0].id
}

output "bastion_public_ip" {
  value = aws_instance.e1[0].public_ip
}

output "bastion_private_ip" {
  value = aws_instance.e1[0].private_ip
}

output "private_file_pem" {
  value = tls_private_key.pk[0].private_key_pem
  sensitive = true
}