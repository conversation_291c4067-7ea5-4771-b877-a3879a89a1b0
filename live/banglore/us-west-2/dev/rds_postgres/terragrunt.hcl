locals {
  config = jsondecode(file(find_in_parent_folders("env.json")))
  environment = local.config.env
  aws_region = local.config.aws_region
  project_name = local.config.project_name
  eks_cluster_name = "${local.environment}-${local.project_name}-eks"
  branch = local.config.branch
  developer = local.config.developer
}

include "root" {
  path = find_in_parent_folders()
  expose = true
}

terraform {
  source = "git::ssh://*****************/xstrela/xstrela-terragrunt-modules.git//modules/rds_postgres?ref=v1.0.1"
}  

dependency "vpc" {
  config_path = "../vpc"

  mock_outputs = {
    vpc_id = "temporary-dummy-id"
    private_data_subnets = ["vpded","eefef-423"]
  }
  
}


inputs = {
  vpc_id   = dependency.vpc.outputs.vpc_id
  subnet_ids    = dependency.vpc.outputs.private_data_subnets
  environment   = local.environment
  project_name  = local.project_name
  name          = local.environment == local.branch ? "${local.environment}-${local.project_name}" : "${local.environment}-${local.project_name}-${local.branch}"
  rds_instances = {
    "admin" = {
      identifier        = "admin"
      allocated_storage = 20
      instance_class    = "db.t3.micro"
      engine            = "postgres"
      engine_version    = "16.8"
      username          = "postgres"
    },
    "cron" = {
      identifier        = "cron"
      allocated_storage = 20
      instance_class    = "db.t3.micro"
      engine            = "postgres"
      engine_version    = "16.8"
      username          = "postgres"
    },
    "game" = {
      identifier        = "game"
      allocated_storage = 20
      instance_class    = "db.t3.micro"
      engine            = "postgres"
      engine_version    = "16.8"
      username          = "postgres"
    },
    "launchpad" = {
      identifier        = "launchpad"
      allocated_storage = 20
      instance_class    = "db.t3.micro"
      engine            = "postgres"
      engine_version    = "16.8"
      username          = "postgres"
    },
    "quest" = {
      identifier        = "quest"
      allocated_storage = 20
      instance_class    = "db.t3.micro"
      engine            = "postgres"
      engine_version    = "16.8"
      username          = "postgres"
    },
    "user" = {
      identifier        = "user"
      allocated_storage = 20
      instance_class    = "db.t3.micro"
      engine            = "postgres"
      engine_version    = "16.8"
      username          = "postgres"
    },
    "web_carousel" = {
      identifier        = "web-carousel"
      allocated_storage = 20
      instance_class    = "db.t3.micro"
      engine            = "postgres"
      engine_version    = "16.8"
      username          = "postgres"
    },
    "gateway" = {
      identifier        = "gateway"
      allocated_storage = 20
      instance_class    = "db.t3.micro"
      engine            = "postgres"
      engine_version    = "16.8"
      username          = "postgres"
    },
    "game_genome" = {
      identifier        = "game-genome"
      allocated_storage = 20
      instance_class    = "db.t3.micro"
      engine            = "postgres"
      engine_version    = "16.8"
      username          = "postgres"
    },
    "notification" = {
      identifier        = "notification"
      allocated_storage = 20
      instance_class    = "db.t3.micro"
      engine            = "postgres"
      engine_version    = "16.8"
      username          = "postgres"
    }

  }
}

