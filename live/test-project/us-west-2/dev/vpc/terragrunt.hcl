include "root" {
  path = find_in_parent_folders()
  expose = true
}

# generate = local.gen_provider.generate

locals {
  config = jsondecode(file(find_in_parent_folders("env.json")))
  environment = local.config.env
  aws_region = local.config.aws_region
  project_name = local.config.project_name
  branch = local.config.branch
  developer = local.config.developer
  eks_cluster_name = "${local.environment}-${local.project_name}-eks"
  vpc_name = local.environment == local.branch ? "${local.environment}-${local.project_name}" : "${local.environment}-${local.project_name}-${local.branch}"

  common_tags = tomap({
    "project" = local.project_name,
    "environment" = local.environment
  })
  # gen_provider   = read_terragrunt_config("${get_repo_root()}/live/terragrunt.hcl")

}

terraform {
  # source = "../../../../../modules/vpc"
  source = "git::ssh://*****************/xstrela/xstrela-terragrunt-modules.git//modules/vpc?ref=v1.0.0"
}

inputs = {
  environment = local.environment
  project_name = local.project_name
  aws_region = local.aws_region
  cidr_block = "********/16"
  name = local.vpc_name
  
  tags = merge(
    local.common_tags,
    {
      "kubernetes.io/cluster/${local.eks_cluster_name}" = "shared"
      "k8s.io/cluster-autoscaler/${local.eks_cluster_name}" = "owned"
      "k8s.io/cluster-autoscaler/enabled" = "true"
      "branch" = "${local.branch}"
      "developer" = "${local.developer}"
      "ManagedBy"  = "terragrunt"
      "CreatedBy" = "ci-cd-pipeline"
    }
  )

  eks_cluster_name = local.eks_cluster_name
  
  private_subnet_tags = {
    "kubernetes.io/cluster/${local.environment}-${local.project_name}-eks" = "owned"
    "kubernetes.io/role/internal-elb" = "1"
  }
  
  public_subnet_tags = {
    "kubernetes.io/role/elb" = "1"
  }
}