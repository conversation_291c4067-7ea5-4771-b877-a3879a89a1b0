terraform {
  source = "./"
}
include "root" {
  path = find_in_parent_folders()
  expose = true
}

locals {
  config = jsondecode(file(find_in_parent_folders("env.json")))
  certificate_region = local.config.aws_region
}

dependency "eks" {
  config_path = "../eks"
  mock_outputs = {
    cluster_name = "temporary-dummy-id"
  }
#   skip_outputs = false
#   mock_outputs_allowed_terraform_commands = ["init", "validate", "plan", "destroy"]
  
}

inputs = {
  cluster_name    = dependency.eks.outputs.cluster_name
  certificate_region = local.certificate_region
}