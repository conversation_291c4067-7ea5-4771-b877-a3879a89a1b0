locals {
  config = jsondecode(file(find_in_parent_folders("env.json")))
  environment = local.config.env
  aws_region = local.config.aws_region
  project_name = local.config.project_name
  branch = local.config.branch
  developer = local.config.developer
  eks_cluster_name = "${local.environment}-${local.project_name}-${local.branch}-eks"
}

include "root" {
  path = find_in_parent_folders()
  expose = true
}

dependencies {
  paths = ["../eks", "../vpc","../datasources"]
}

dependency "eks" {
  config_path = "../eks"
  mock_outputs = {
    cluster_id = "temporary-dummy-id"
    cluster_identity_oidc_issuer_arn ="eeededed"
    cluster_identity_oidc_issuer = "eedeefe"
    cluster_oidc_issuer_url = "dddde"
    endpoint ="eeeddde"
    cluster_certificate_authority_data = "eeeee"
  }
#   skip_outputs = false
  mock_outputs_allowed_terraform_commands = ["init", "validate", "plan", "destroy"]
  
}

 dependency "data" {
    config_path = "../datasources"
    mock_outputs = {
    aws_region  = local.config.aws_region
    ssl_cert = "eeeee"
  }
    mock_outputs_allowed_terraform_commands = ["init", "validate", "plan", "destroy"]

 }

terraform {
  # source = "../../../../../modules/nginx_ingress"
  source = "git::ssh://*****************/xstrela/xstrela-terragrunt-modules.git//modules/nginx_ingress?ref=v1.0.0"
}  

dependency "vpc" {
  config_path = "../vpc"

  mock_outputs = {
    vpc_id = "temporary-dummy-id"
    public_subnets = ["vpded","eefef-423"]
  }
  
}

inputs = {
  cluster_name    = local.eks_cluster_name
  public_subnet    = dependency.vpc.outputs.public_subnets
  aws_region        = dependency.data.outputs.aws_region
  ssl_cert =  dependency.data.outputs.ssl_cert
}

generate "provider-local" {
  path      = "provider-local.tf"
  if_exists = "overwrite"
  contents  = <<EOF
  
provider "kubernetes" {
  host                   = data.aws_eks_cluster.cluster.endpoint
  cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority[0].data)
  exec {
    api_version = "client.authentication.k8s.io/v1beta1"
    command     = "aws"
    args        = ["eks", "get-token", "--cluster-name", "${local.eks_cluster_name}"]
  }
}

provider "helm" {
  kubernetes = {
    host                   = data.aws_eks_cluster.cluster.endpoint
    cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority[0].data)
    exec = {
      api_version = "client.authentication.k8s.io/v1beta1"
      command     = "aws"
      args        = ["eks", "get-token", "--cluster-name", "${local.eks_cluster_name}"]
    }
  }
}

data "aws_eks_cluster" "cluster" {
  name = "${local.eks_cluster_name}"
}

data "aws_eks_cluster_auth" "cluster" {
  name = "${local.eks_cluster_name}"
}
EOF
}