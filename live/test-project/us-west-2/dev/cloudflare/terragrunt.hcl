locals {
  config = jsondecode(file(find_in_parent_folders("env.json")))
  environment = local.config.env
  aws_region = local.config.aws_region
  project_name = local.config.project_name
  branch = local.config.branch
  developer = local.config.developer
}

include "root" {
  path = find_in_parent_folders()
  expose = true
}

dependencies {
  paths = ["../admin", "../users"]
}

dependency "admin" {
  config_path = "../admin"
  mock_outputs = {
    domain_associations = {
      "admin" = {
        "domain_name" = "admin-test.phynd.games"
        "certificate_verification_dns_record" = "_mock123.admin-test.phynd.games. CNAME _mock456.xlfgrmvvlj.acm-validations.aws."
        "sub_domain" = [
          {
            "branch_name" = "test"
            "dns_record" = " CNAME d1234567890.cloudfront.net"
            "prefix" = ""
          }
        ]
      }
    }
    certificate_verification_dns_records = {
      "admin" = {
        "name" = "_mock123.admin-test.phynd.games"
        "type" = "CNAME"
        "value" = "_mock456.xlfgrmvvlj.acm-validations.aws"
      }
    }
    domain_routing_records = [
      {
        "domain" = "admin-test.phynd.games"
        "name" = "@"
        "type" = "CNAME"
        "value" = "d1234567890.cloudfront.net"
        "branch" = "test"
      }
    ]
  }
  mock_outputs_allowed_terraform_commands = ["init", "validate", "plan", "destroy"]
}

dependency "users" {
  config_path = "../users"
  mock_outputs = {
    domain_associations = {
      "users" = {
        "domain_name" = "test.phynd.games"
        "certificate_verification_dns_record" = "_mock789.test.phynd.games. CNAME _mock012.xlfgrmvvlj.acm-validations.aws."
        "sub_domain" = [
          {
            "branch_name" = "test"
            "dns_record" = " CNAME d0987654321.cloudfront.net"
            "prefix" = ""
          }
        ]
      }
    }
    certificate_verification_dns_records = {
      "users" = {
        "name" = "_mock789.test.phynd.games"
        "type" = "CNAME"
        "value" = "_mock012.xlfgrmvvlj.acm-validations.aws"
      }
    }
    domain_routing_records = [
      {
        "domain" = "test.phynd.games"
        "name" = "@"
        "type" = "CNAME"
        "value" = "d0987654321.cloudfront.net"
        "branch" = "test"
      }
    ]
  }
}
 
 terraform {
   source = "git::ssh://*****************/xstrela/xstrela-terragrunt-modules.git//modules/cloudflare?ref=v1.0.0"
 }
 
 inputs = {
   # Cloudflare configuration
    cloudflare_api_token = get_env("cloudflare_api_token","")
   
   zone_id = get_env("zone_id","")
   
   # Merge certificate verification records from both admin and users
   certificate_verification_records = merge(
     dependency.admin.outputs.certificate_verification_dns_records,
     dependency.users.outputs.certificate_verification_dns_records
   )
   
   # Combine domain routing records from both admin and users
   domain_routing_records = concat(
     dependency.admin.outputs.domain_routing_records,
     dependency.users.outputs.domain_routing_records
   )
   
 }
  # mock_outputs_allowed_terraform_commands = ["init", "validate", "plan", "destroy"]


 

