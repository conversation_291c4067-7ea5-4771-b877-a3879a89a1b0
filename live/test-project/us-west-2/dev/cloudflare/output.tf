# output "domain_associations" {
#   description = "Created domain associations"
#   value       = aws_amplify_domain_association.default
# }

# output "certificate_verification_dns_records" {
#   description = "DNS records needed for certificate verification"
#   value = {
#     for k, v in aws_amplify_domain_association.default : k => {
#       name = split(" ", v.certificate_verification_dns_record)[0]
#       type = "CNAME"
#       value = split(" ", v.certificate_verification_dns_record)[2]
#     }
#   }
# }

# output "domain_routing_records" {
#   description = "DNS records needed for domain routing"
#   value = flatten([
#     for domain_key, domain in aws_amplify_domain_association.default : [
#       for subdomain in domain.sub_domain : {
#         domain = domain.domain_name
#         name = subdomain.branch_name
#         type = "CNAME"
#         value = trim(split("CNAME", subdomain.dns_record)[1]," ")
#         branch = subdomain.branch_name
#       }
#     ]
#   ])
# }

