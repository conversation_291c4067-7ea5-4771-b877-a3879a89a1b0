locals {
  config = jsondecode(file(find_in_parent_folders("env.json")))
  environment = local.config.env
  aws_region = local.config.aws_region
  project_name = local.config.project_name
  branch = local.config.branch
  developer = local.config.developer
  eks_cluster_name = "${local.environment}-${local.project_name}-eks"
  common_tags = tomap({
    "project" = local.project_name,
    "environment" = local.environment
  })
}

include "root" {
  path = find_in_parent_folders()
  expose = true
}

terraform {
  # source = "../../../../../modules/kafka"
  source = "git::ssh://*****************/xstrela/xstrela-terragrunt-modules.git//modules/kafka?ref=v1.0.1"
}  

dependency "vpc" {
  config_path = "../vpc"

  mock_outputs = {
    vpc_id = "temporary-dummy-id"
    private_data_subnets = ["vpded","eefef-423"]
  }
  
}


inputs = {
  vpc_id   = dependency.vpc.outputs.vpc_id
  subnet_ids   = dependency.vpc.outputs.private_data_subnets
  environment   = local.environment
  project_name  = local.project_name
  name = local.environment == local.branch ? "${local.environment}-${local.project_name}" : "${local.environment}-${local.project_name}-${local.branch}"
  kafka_version = "3.6.0"
  broker_instance_type = "kafka.t3.small"
  tags = merge(
    local.common_tags,
    {
      "ManagedBy"  = "terragrunt"
      "CreatedBy" = "ci-cd-pipeline"
      "branch" = "${local.branch}"
      "developer" = "${local.developer}"
    },
  )

}