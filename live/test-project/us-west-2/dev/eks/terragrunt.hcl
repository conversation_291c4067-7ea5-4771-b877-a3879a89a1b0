include "root" {
  path = find_in_parent_folders()
  expose = true
}

locals {
  config = jsondecode(file(find_in_parent_folders("env.json")))
  environment = local.config.env
  aws_region = local.config.aws_region
  project_name = local.config.project_name
  branch = local.config.branch
  developer = local.config.developer
  eks_cluster_name = local.environment == local.branch ? "${local.environment}-${local.project_name}-eks" : "${local.environment}-${local.project_name}-${local.branch}-eks"
  common_tags = tomap({
    "project" = local.project_name,
    "environment" = local.environment
  })
  key_name = local.environment == local.branch ? "${local.environment}-${local.project_name}" : "${local.environment}-${local.project_name}-${local.branch}"
}

dependency "vpc" {
  config_path = "../vpc"

  mock_outputs = {
    # vpc_id = "temporary-dummy-id"
    # new_output = "temporary-dummy-value"
    vpc_id = "temporary-dummy-id"
    private_subnets = ["vpded","eefef-423"]
  }
  
  # mock_outputs_merge_strategy_with_state = "shallow"
  # skip_outputs = true
  # mock_outputs_allowed_terraform_commands = ["validate","plan","apply"]
}
inputs = {
  vpc_id = dependency.vpc.outputs.vpc_id
  key_name = local.key_name
  cluster_name    = local.eks_cluster_name
  cluster_version = "1.33"
  subnets         = dependency.vpc.outputs.private_subnets
  enable_irsa = true
  tags = merge(
    local.common_tags,
    {
      "kubernetes.io/cluster/${local.eks_cluster_name}"     = "shared"
      "k8s.io/cluster-autoscaler/${local.eks_cluster_name}" = "owned"
      "k8s.io/cluster-autoscaler/enabled"                   = "true"
      "branch" = "${local.branch}"
      "developer" = "${local.developer}"
    },
  )
}
terraform {
  # source = "../../../../../modules/eks"
  source = "git::ssh://*****************/xstrela/xstrela-terragrunt-modules.git//modules/eks?ref=v1.0.1"
}



