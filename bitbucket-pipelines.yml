image: ubuntu:latest

definitions:
  scripts:
    - &sshsetup |
      which ssh-agent || ( apt-get update -y && apt-get install openssh-client -y )
      eval $(ssh-agent -s)
      mkdir -p ~/.ssh
      chmod 700 ~/.ssh
      echo "$SSH_PRIVATE_KEY" | base64 -d > ~/.ssh/id_rsa
      chmod 600 ~/.ssh/id_rsa
      ssh-add ~/.ssh/id_rsa
      ssh-keyscan -H github.com >> ~/.ssh/known_hosts
      ssh-keyscan -H bitbucket.org >> ~/.ssh/known_hosts

    - &install-terraform |
      #Install Terraform & Terragrunt
      apt-get update && apt-get install -y wget unzip git jq
      wget https://releases.hashicorp.com/terraform/1.12.2/terraform_1.12.2_linux_amd64.zip
      unzip terraform_1.12.2_linux_amd64.zip -d /usr/local/bin/
      terraform --version  # Debug: Verify installation
      
      wget https://github.com/gruntwork-io/terragrunt/releases/download/v0.83.0/terragrunt_linux_amd64
      chmod +x terragrunt_linux_amd64
      mv terragrunt_linux_amd64 /usr/local/bin/terragrunt
      terragrunt --version
      jq --version  # Debug: Verify jq installation

    - &configure-aws-profile |
      # Install AWS CLI
       apt-get update && apt-get install -y curl unzip
       curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
       unzip awscliv2.zip
      ./aws/install
      # Configure AWS CLI
      aws configure set aws_access_key_id $AWS_ACCESS_KEY_ID --profile deployment
      aws configure set aws_secret_access_key $AWS_SECRET_ACCESS_KEY --profile deployment
      aws configure set region $AWS_DEFAULT_REGION --profile deployment

pipelines:
  branches:
    live-branch:
      - step:
          name: Dynamic Infrastructure Deployment
          image: ubuntu:latest
          size: 2x
          script:
            - *sshsetup
            - *install-terraform
            - *configure-aws-profile
            - export bitbucket_oauth_client_id=$bitbucket_oauth_client_id
            - export bitbucket_oauth_secret=$bitbucket_oauth_secret
            - export cloudflare_api_token=$cloudflare_api_token
            - export zone_id=$zone_id
            - |
              # Get parameters from pipeline variables
              DEPLOYMENT_ENV=${DEPLOYMENT_ENV:-"dev"}
              PROJECT_NAME=${PROJECT_NAME:-"default-project"}
              AWS_REGION=${AWS_REGION:-"us-east-1"}
              BRANCH=${BRANCH:-"dev"}
              DEVELOPER=${DEVELOPER:-"dev"}
              ACTION=${ACTION:-"apply"}
              
              echo "Pipeline Variables:"
              echo "  Environment: $DEPLOYMENT_ENV"
              echo "  Project: $PROJECT_NAME"
              echo "  Region: $AWS_REGION"
              echo "  Action: $ACTION"
              echo "  Branch: $BRANCH"
              echo "  Developer: $DEVELOPER"
              
              # Create config.env for backward compatibility with deploy.sh
              cat > config.env << EOF
              aws_region="$AWS_REGION"
              project_name="$PROJECT_NAME"
              action=$ACTION
              environment="$DEPLOYMENT_ENV"
              branch=$BRANCH
              developer=$DEVELOPER
              EOF
              
              echo "Executing $ACTION for $DEPLOYMENT_ENV environment"
              if [ "$ACTION" == "apply" ] || [ "$ACTION" == "deploy" ]; then
                chmod +x deploy.sh
                ./deploy.sh "$DEPLOYMENT_ENV"
              else
                chmod +x destroy.sh
                ./destroy.sh "$DEPLOYMENT_ENV"
              fi
          artifacts:
            paths:
              - logs/**
              - artifacts/**

              