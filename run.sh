#!/bin/bash

# Simple script to trigger Bitbucket pipeline with parameters as variables
# Usage: ./run.sh --deploy qa --project myapp --region us-east-1 --action apply

set -e

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Default values
ACTION=""
ENVIRONMENT=""
PROJECT_NAME=""
AWS_REGION=""
BRANCH=""
DEVELOPER=""

# Bitbucket configuration (set these as environment variables)
BITBUCKET_WORKSPACE="${BITBUCKET_WORKSPACE}"
BITBUCKET_REPO="${BITBUCKET_REPO}"
BITBUCKET_USERNAME="${BITBUCKET_USERNAME}"
BITBUCKET_APP_PASSWORD="${BITBUCKET_APP_PASSWORD}"

show_usage() {
    cat << EOF
Usage: $0 --deploy <env> --project <name> --region <region> --branch <branch> --developer <developer> --action <apply|destroy>

Example:
    $0 --deploy qa --project myapp --region us-east-1 --branch feature-1 --developer mayank --action apply

Environment Variables needed:
    BITBUCKET_WORKSPACE, BITBUCKET_REPO, BITBUCKET_USERNAME, BITBUCKET_APP_PASSWORD
EOF
}

# Parse arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --deploy)
            ENVIRONMENT="$2"
            shift 2
            ;;
        --project)
            PROJECT_NAME="$2"
            shift 2
            ;;
        --region)
            AWS_REGION="$2"
            shift 2
            ;;
        --branch)
            BRANCH="$2"
            shift 2
            ;;
        --developer)
            DEVELOPER="$2"
            shift 2
            ;;    
        --action)
            ACTION="$2"
            shift 2
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate inputs
if [[ -z "$ENVIRONMENT" || -z "$PROJECT_NAME" || -z "$AWS_REGION" || -z "$BRANCH" || -z "$DEVELOPER" || -z "$ACTION" ]]; then
    print_error "Missing required parameters"
    show_usage
    exit 1
fi

# Check Bitbucket credentials
if [[ -z "$BITBUCKET_WORKSPACE" || -z "$BITBUCKET_REPO" || -z "$BITBUCKET_USERNAME" || -z "$BITBUCKET_APP_PASSWORD" ]]; then
    print_error "Missing Bitbucket credentials. Set these environment variables:"
    echo "  export BITBUCKET_WORKSPACE='your-workspace'"
    echo "  export BITBUCKET_REPO='your-repo-name'"
    echo "  export BITBUCKET_USERNAME='your-username'"
    echo "  export BITBUCKET_APP_PASSWORD='your-app-password'"
    exit 1
fi

# Show what we're about to do
print_status "Pipeline Parameters:"
echo "  Environment: $ENVIRONMENT"
echo "  Project: $PROJECT_NAME"
echo "  Region: $AWS_REGION"
echo "  Action: $ACTION"
echo "  Branch: $BRANCH"
echo "  Developer: $DEVELOPER"
echo

# Trigger the pipeline with variables
print_status "Triggering pipeline on live branch..."

API_URL="https://api.bitbucket.org/2.0/repositories/$BITBUCKET_WORKSPACE/$BITBUCKET_REPO/pipelines/"

# Create payload with all parameters as pipeline variables
PAYLOAD='{
    "target": {
        "ref_type": "branch",
        "type": "pipeline_ref_target",
        "ref_name": "live-branch"
    },
    "variables": [
        {
            "key": "DEPLOYMENT_ENV",
            "value": "'$ENVIRONMENT'"
        },
        {
            "key": "PROJECT_NAME",
            "value": "'$PROJECT_NAME'"
        },
        {
            "key": "AWS_REGION",
            "value": "'$AWS_REGION'"
        },
        {
            "key": "BRANCH",
            "value": "'$BRANCH'"
        },
        {
            "key": "DEVELOPER",
            "value": "'$DEVELOPER'"
        },
        {
            "key": "ACTION",
            "value": "'$ACTION'"
        }
    ]
}'

# Make the API call
print_status "Sending pipeline trigger request..."
RESPONSE=$(curl -s -X POST "$API_URL" \
    -u "$BITBUCKET_USERNAME:$BITBUCKET_APP_PASSWORD" \
    -H "Content-Type: application/json" \
    -d "$PAYLOAD")

# Check if successful
if echo "$RESPONSE" | grep -q '"state"'; then
    BUILD_NUMBER=$(echo "$RESPONSE" | grep -o '"build_number":[0-9]*' | cut -d':' -f2)
    print_success "Pipeline triggered successfully!"
    print_status "Build #$BUILD_NUMBER started"
    print_status "Parameters: $PROJECT_NAME-$ENVIRONMENT ($ACTION) in $AWS_REGION"
    print_status "View at: https://bitbucket.org/$BITBUCKET_WORKSPACE/$BITBUCKET_REPO/pipelines"
else
    print_error "Failed to trigger pipeline"
    echo "Response: $RESPONSE"
    exit 1
fi