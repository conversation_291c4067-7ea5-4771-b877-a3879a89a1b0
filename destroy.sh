#!/bin/bash

# Exit on error and enable debugging
set -e
set -x

# Navigate to the directory where the script is located
cd "$(dirname "$0")"

# Source the config file
if [ -f "config.env" ]; then
    source config.env
else
    echo "Error: config.env not found!"
    exit 1
fi

# Ensure Terragrunt is installed
if ! command -v terragrunt &> /dev/null; then
    echo "Terragrunt is not installed. Exiting..."
    exit 1
fi

# Get the environment from the command-line argument
env="$1"
if [ -z "$env" ]; then
    echo "Error: No environment provided as argument!"
    exit 1
fi

destroy_environment() {
  env=$1
  project_name=$2
  aws_region=$3
  branch=$4
  developer=$5
  echo "Starting destruction for environment: $env..."

  DEST_DIR="live/$project_name/$aws_region/$env"

  if [ -d "$DEST_DIR" ]; then
      echo "Skipping copy, $DEST_DIR already exists."
  else
        mkdir -p "$DEST_DIR"
        echo "Copying contents from (default project) live/default-project/us-east-1 to live/$project_name/$aws_region/$env..."
        cp -r live/default-project/us-east-1/dev/* "$DEST_DIR/"
        echo "Copy completed for $env."

  fi

  cat <<EOF > "$DEST_DIR/env.json"
{
  "env": "$env",
  "aws_region": "$aws_region",
  "project_name": "$project_name",
  "branch": "$branch",
  "developer": "$developer"
}
EOF

echo "Updated env.json for $env in $DEST_DIR"

  check_bucket_exists "$env" 
  check_dynamodb_table_exists "$env" "$developer" "$branch"
}

# Function to check if S3 bucket exists
check_bucket_exists() {
    bucket_name="$project_name-$1-$aws_region"
    echo "Checking if bucket $bucket_name exists..."

    if aws s3api head-bucket --bucket "$bucket_name" 2>/dev/null; then
        echo "Bucket $bucket_name exists. No action needed."
    else
        echo "Bucket $bucket_name does not exist."
    fi
}

# Function to check if DynamoDB table exists
check_dynamodb_table_exists() {
    table_name="$project_name-$1-$2-$3-$aws_region-dynamodb"
    echo "Checking if DynamoDB table $table_name exists in region $aws_region..."

    if aws dynamodb describe-table --table-name "$table_name" --region "$aws_region" 2>/dev/null; then
        echo "DynamoDB table $table_name exists. No action needed."
    else
        echo "DynamoDB table $table_name does not exist."
    fi
}

delete_secret_manager_env() {
    env=$1
    project_name=$2
    aws_region=$3
    branch=$4
    developer=$5
    
    echo "Deleting secret from Secrets Manager for environment: $env..."

    if[$env == $branch]; then
    secret_name="$project_name/$aws_region/$env"
  else
    secret_name="$project_name/$aws_region/$env/$branch"
  fi
    
    # Secret name in format project_name/region/env
    # secret_name="$project_name/$aws_region/$env/$branch"
    
    # Check if the secret exists
    if aws secretsmanager describe-secret --secret-id "$secret_name" --region "$aws_region" 2>/dev/null; then
        echo "Secret $secret_name exists. Deleting..."
        
        # Delete the secret with force delete (skip recovery window)
        aws secretsmanager delete-secret \
            --secret-id "$secret_name" \
            --force-delete-without-recovery \
            --region "$aws_region"
            
        echo "Secret $secret_name deleted successfully."
    else
        echo "Secret $secret_name does not exist. No action needed."
    fi
}


# Start destruction for all environments
echo "Starting destruction for environments: $env"
if ! destroy_environment "$env" "$project_name" "$aws_region" "$branch" "$developer"; then
      echo "Error encountered during destruction for $env. Aborting further operations."
      exit 1
fi


echo "Running Terragrunt for given environments..."

# all_env_dirs=$(ls -d live/"${project_name}"/"${aws_region}"/*/)
# exclude_dirs=""

# for dir in $all_env_dirs; do
#   env_name=$(basename "$dir")
#   if [[ ! " ${environments[@]} " =~ " ${env_name} " ]]; then
#     exclude_dirs="${exclude_dirs} --terragrunt-exclude-dir=**/${env_name}/**"
#   fi
# done

mkdir -p logs
# if [ -n "$exclude_dirs" ]; then
#   echo "Excluding directories: $exclude_dirs"
#   terragrunt run-all destroy  --terragrunt-working-dir live/"${project_name}"/"${aws_region}"/ --terragrunt-disable-bucket-update=true -auto-approve --terragrunt-non-interactive $exclude_dirs 2>&1 | tee logs/terragrunt.log
#   TERRAGRUNT_EXIT_STATUS=${PIPESTATUS[0]}
# else
#   echo "No directories to exclude. Running Terragrunt for all selected environments."
#   terragrunt run-all destroy  --terragrunt-working-dir live/"${project_name}"/"${aws_region}"/ --terragrunt-disable-bucket-update=true -auto-approve --terragrunt-non-interactive 2>&1 | tee logs/terragrunt.log
#   TERRAGRUNT_EXIT_STATUS=${PIPESTATUS[0]}
# fi

terragrunt run-all destroy  --terragrunt-working-dir live/"${project_name}"/"${aws_region}"/"${env}"/ --terragrunt-disable-bucket-update=true -auto-approve --terragrunt-non-interactive 2>&1 | tee logs/terragrunt.log
TERRAGRUNT_EXIT_STATUS=${PIPESTATUS[0]}

echo "Deleting secrets from Secrets Manager for environments: ${env}"
# for env in "${environments[@]}"; do
#     if ! delete_secret_manager_env "$env" "$project_name" "$aws_region"; then
#         echo "Error encountered while deleting secret for $env. Continuing with other environments..."
#     fi
# done

if ! delete_secret_manager_env "$env" "$project_name" "$aws_region"; then
    echo "Error encountered while deleting secret for $env. Continuing with other environments..."
    exit 1
fi


if [ $TERRAGRUNT_EXIT_STATUS -ne 0 ]; then
    echo "Terragrunt deployment failed with exit status $TERRAGRUNT_EXIT_STATUS"
    exit $TERRAGRUNT_EXIT_STATUS
fi


echo "Destruction completed successfully."
