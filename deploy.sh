#!/bin/bash

# Exit on error and enable debugging
set -e
set -x

# Navigate to the directory where the script is located
cd "$(dirname "$0")"

# Source the config file
if [ -f "config.env" ]; then
    source config.env
else
    echo "Error: config.env not found!"
    exit 1
fi

# Ensure Terragrunt is installed
if ! command -v terragrunt &> /dev/null; then
    echo "Terragrunt is not installed. Exiting..."
    exit 1
fi

# Get the environment from the command-line argument
env="$1"
if [ -z "$env" ]; then
    echo "Error: No environment provided as argument!"
    exit 1
fi

deploy_environment() {
  env=$1
  project_name=$2
  aws_region=$3
  branch=$4
  developer=$5

  echo "Starting deployment for environment: $env..."

  # Create environment directory if not exists
  DEST_DIR="live/$project_name/$aws_region/$env"

  # Skip copying for dev environment
  if [ -d "$DEST_DIR" ]; then
      echo "Skipping copy, $DEST_DIR already exists."
  else
        mkdir -p "$DEST_DIR"
        echo "Copying contents from (default project) live/default-project/us-east-1 to live/$project_name/$aws_region/$env..."
        cp -r live/default-project/us-east-1/dev/* "$DEST_DIR/"
        echo "Copy completed for $env."
  fi

  cat <<EOF > "$DEST_DIR/env.json"
{
  "env": "$env",
  "aws_region": "$aws_region",
  "project_name": "$project_name",
  "branch": "$branch",
  "developer": "$developer"
}
EOF

  echo "Updated env.json for $env in $DEST_DIR"

  check_or_create_bucket "$env" 
  check_or_create_dynamodb_table "$env" "$developer" "$branch"
}

# Function to check or create S3 bucket
check_or_create_bucket() {
    bucket_name="$project_name-$1-$aws_region"
    echo "Checking if bucket $bucket_name exists..."

    if aws s3api head-bucket --bucket "$bucket_name" 2>/dev/null; then
        echo "Bucket $bucket_name already exists."
    else
        echo "Creating bucket $bucket_name..."
        if [ "$aws_region" = "us-east-1" ]; then
            aws s3api create-bucket \
                --bucket "$bucket_name" \
                --region "$aws_region"
        else
            aws s3api create-bucket \
                --bucket "$bucket_name" \
                --region "$aws_region" \
                --create-bucket-configuration LocationConstraint="$aws_region"
        fi
        echo "Enabling versioning for bucket $bucket_name..."
        aws s3api put-bucket-versioning --bucket "$bucket_name" --versioning-configuration Status=Enabled
    fi
}

# Function to check or create DynamoDB table
check_or_create_dynamodb_table() {
    table_name="$project_name-$1-$2-$3-$aws_region-dynamodb"
    echo "Checking if DynamoDB table $table_name exists in region $aws_region..."

    if aws dynamodb describe-table --table-name "$table_name" --region "$aws_region" 2>/dev/null; then
        echo "DynamoDB table $table_name already exists."
    else
        echo "Creating DynamoDB table $table_name..."
        aws dynamodb create-table \
            --table-name "$table_name" \
            --attribute-definitions AttributeName=LockID,AttributeType=S \
            --key-schema AttributeName=LockID,KeyType=HASH \
            --billing-mode PAY_PER_REQUEST \
            --region "$aws_region"

        echo "Waiting for DynamoDB table $table_name to be active..."
    fi
}

secret_manager_env() {
  env=$1
  project_name=$2
  aws_region=$3
  branch=$4
  developer=$5
  
  echo "Storing private key in Secrets Manager for environment: $env..."

  if[$env == $branch]; then
    secret_name="$project_name/$aws_region/$env"
  else
    secret_name="$project_name/$aws_region/$env/$branch"
  fi
  
  # Create a secret name in format project_name/region/env
#   secret_name="$project_name/$aws_region/$env/$branch"
  
  # Temporarily turn off command echo to hide sensitive information
  set +x
  
  # Get the private key from terragrunt output
  echo "Retrieving private key for $env (output hidden)..."
  private_key=$(terragrunt output -raw private_file_pem --terragrunt-working-dir live/$project_name/$aws_region/$env/vpc)
  if [ $? -ne 0 ]; then
      echo "Failed to retrieve private key for $env. Exiting..."
      # Turn debug back on
      set -x
      exit 1
  fi
  
  # Get the database passwords from terragrunt output
  echo "Retrieving database passwords for $env (output hidden)..."
  db_passwords_json=$(terragrunt output -json db_passwords --terragrunt-working-dir live/$project_name/$aws_region/$env/rds_postgres 2>/dev/null)
  
  # Create combined secret JSON
  if [ $? -eq 0 ] && [ -n "$db_passwords_json" ]; then
    echo "Combining private key and database passwords into single secret..."
    # Create JSON with both private key and database passwords
    combined_secret=$(jq -n \
      --arg private_key "$private_key" \
      --argjson db_passwords "$db_passwords_json" \
      '{private_key: $private_key, db_passwords: $db_passwords}')
  else
    echo "No database passwords found, storing only private key..."
    # Create JSON with only private key
    combined_secret=$(jq -n --arg private_key "$private_key" '{private_key: $private_key}')
  fi
  
  # Check if the secret already exists
  echo "Checking if secret $secret_name exists..."
  if aws secretsmanager describe-secret --secret-id "$secret_name" --region "$aws_region" 2>/dev/null; then
      echo "Secret $secret_name already exists. Updating value (content hidden)..."
      aws secretsmanager update-secret --secret-id "$secret_name" \
          --secret-string "$combined_secret" \
          --region "$aws_region" > /dev/null
  else
      echo "Creating new secret $secret_name (content hidden)..."
      aws secretsmanager create-secret --name "$secret_name" \
          --description "Private key and database passwords for $project_name $env environment" \
          --secret-string "$combined_secret" \
          --region "$aws_region" > /dev/null
  fi
  
  # Turn debug back on
  set -x
  
  echo "Successfully stored private key and database passwords in Secrets Manager for $env"
}

# Start deployment for the specified environment
echo "Starting deployment for environment: $env"
if ! deploy_environment "$env" "$project_name" "$aws_region" "$branch" "$developer"; then
    echo "Error encountered during deployment for $env. Aborting further operations."
    exit 1
fi


# Git commit and push changes
if [[ -n "$(git status --porcelain live/)" ]]; then
  echo "Committing and pushing changes to Git with [skip ci]..."
  git add live/
  git add  -u live/
  git commit -m "Updated env deployment for  $env [skip ci]"
  git push origin "$(git rev-parse --abbrev-ref HEAD)"
else
  echo "No changes to commit. Skipping commit and push."
fi

# Run Terragrunt
echo "Running Terragrunt for environment: $env..."
mkdir -p logs
terragrunt run-all apply --terragrunt-working-dir live/"${project_name}"/"${aws_region}"/"${env}" --terragrunt-disable-bucket-update=true -auto-approve --terragrunt-non-interactive 2>&1 | tee logs/terragrunt.log
TERRAGRUNT_EXIT_STATUS=${PIPESTATUS[0]}

# Store secrets
echo "Storing artifacts for environment: $env"
secret_manager_env "$env" "$project_name" "$aws_region" "$branch" "$developer"

if [ $TERRAGRUNT_EXIT_STATUS -ne 0 ]; then
    echo "Terragrunt deployment failed with exit status $TERRAGRUNT_EXIT_STATUS"
    exit $TERRAGRUNT_EXIT_STATUS
fi

echo "Deployment completed successfully."
